using Application.DTOs.Booking;
using Application.Exceptions;
using Application.Interfaces.Repositories;
using Application.Wrappers;
using AutoMapper;
using Domain.Entities;
using MediatR;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Application.Features.Bookings.CreateBooking
{
    public partial class BookingCommand : IRequest<Response<BookingResponse>>
    {
      
        public string PickUpCity { get; set; }
        public string DropOffCity { get; set; }
        public string TripType { get; set; }
        public string CarCategory { get; set; }
        public string Duration { get; set; }
        public decimal? Distance { get; set; }  // Billing distance (actual + detour buffer)
        public decimal? ActualDistance { get; set; }  // Actual Google Maps distance
        public decimal? BasicFare { get; set; }
        public decimal? DriverCharge { get; set; }
        public decimal? Gst { get; set; }
        public decimal? Fare { get; set; }
        public decimal? GstFare { get; set; }
        public string CouponCode { get; set; }
        public decimal? CouponDiscount { get; set; }
        public string PickUpAddress { get; set; }
        public string DropOffAddress { get; set; }
        public DateTime? PickUpDate { get; set; }
        public string PickUpTime { get; set; }
        public string TravelerName { get; set; }
        public string PhoneNumber { get; set; }
        public string MailId { get; set; }
        public int? PaymentMode { get; set; }
        public string BookingCreatedBy { get; set; }
        public string RazorpayOrderid { get; set; }
        public string RazorpayStatus { get; set; }
        public string PickUpAddressLongLat { get; set; }
        public string DropOffAddressLongLat { get; set; }
        public decimal CashAmountToPayDriver { get; set; }
        public int PaymentOption { get; set; }
        public decimal? TollCharge { get; set; }
        public string PaymentType { get; set; } // "PARTIAL" or "FULL"
        public decimal? PartialPaymentAmount { get; set; }
        public decimal? RemainingAmountForDriver { get; set; }

        public string? callbackUrl { get; set; }
    }

    public class CreateBookingCommandHandler : IRequestHandler<BookingCommand, Response<BookingResponse>>
    {
        private readonly IBookingRepositoryAsync _bookingRepositoryAsync;
        private readonly IMapper _mapper;
        public CreateBookingCommandHandler(IBookingRepositoryAsync bookingRepositoryAsync, IMapper mapper)
        {
            _bookingRepositoryAsync = bookingRepositoryAsync;
            _mapper = mapper;
        }

        public async Task<Response<BookingResponse>> Handle(BookingCommand request, CancellationToken cancellationToken)
        {
            var bookingRequest = _mapper.Map<RLT_BOOKING>(request);
            var booking = await _bookingRepositoryAsync.AddNewAsync(bookingRequest);
            if (booking == null)
            {
                throw new ApiException($"Booking Not Created.");
            }
            else
            {
                return new Response<BookingResponse>(booking);
            }
        }
    }
}
