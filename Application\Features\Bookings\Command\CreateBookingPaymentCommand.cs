﻿using Application.DTOs.Payment;
using Application.Exceptions;
using Application.Interfaces.Repositories;
using Application.Wrappers;
using AutoMapper;
using MediatR;
using System;
using System.Threading;
using System.Threading.Tasks;
using Application.Interfaces;
using Application.DTOs.Email;
using Application.DTOs.Sms;
using System.Globalization;
using Domain.Settings;
using Microsoft.Extensions.Options;

namespace Application.Features.Bookings.Command
{
    public class CreateBookingPaymentCommand : IRequest<Response<RazorPaymentResponse>>
    {
        public string PaymentId { get; set; }
        public string RazorpayOrderId { get; set; }
        public string RazorpaySignature { get; set; }


        public class CreateBookingPaymentCommandHandler : IRequestHandler<CreateBookingPaymentCommand, Response<RazorPaymentResponse>>
        {
            private readonly IBookingRepositoryAsync _bookingRepositoryAsync;
            private readonly IMapper _mapper;
            private readonly IEmailService _emailService;
            private readonly ISmsService _smsService;
            private readonly ICloudSmsSettings _iCloudSmsSettings;

            public CreateBookingPaymentCommandHandler(
                IBookingRepositoryAsync bookingRepositoryAsync,
                IMapper mapper,
                IEmailService emailService,
                ISmsService smsService,
                IOptions<ICloudSmsSettings> iCloudSmsSettings)
            {
                _bookingRepositoryAsync = bookingRepositoryAsync;
                _mapper = mapper;
                _emailService = emailService;
                _smsService = smsService;
                _iCloudSmsSettings = iCloudSmsSettings.Value;
            }

            public async Task<Response<RazorPaymentResponse>> Handle(CreateBookingPaymentCommand request, CancellationToken cancellationToken)
            {
                var booking = await _bookingRepositoryAsync.RazorPayPaymentSuccess(request.PaymentId, request.RazorpayOrderId, request.RazorpaySignature);
                if (booking == null)
                {
                    throw new ApiException($"Payment Failed.");
                }
                else
                {
                    Console.WriteLine($"Payment Success BookingId: {booking.BookingId}");
                    // Get booking details to include in the email
                    var bookingDetails = await _bookingRepositoryAsync.GetByUniqueIdAsync(booking.BookingId);
                    Console.WriteLine($"Booking Details: {bookingDetails}");

                    if (bookingDetails != null)
                    {
                        // Send confirmation email
                        await SendBookingConfirmationEmail(bookingDetails, booking.TransactionId);

                        // Send confirmation SMS
                        if (!string.IsNullOrEmpty(bookingDetails.PhoneNumber))
                        {
                            await SendBookingConfirmationSms(bookingDetails);

                            // Send admin alert SMS
                            await SendAdminBookingAlertSms(bookingDetails);
                        }
                    }

                    return new Response<RazorPaymentResponse>(booking);
                }
            }

            private async Task SendBookingConfirmationEmail(Domain.Entities.RLT_BOOKING bookingDetails, string transactionId)
            {
                if (string.IsNullOrEmpty(bookingDetails.MailId))
                    return;

                string emailBody = $@"
                <h2>Booking Confirmation</h2>
                <p>Dear {bookingDetails.TravelerName},</p>
                <p>Thank you for booking with CabYaari. Your booking has been confirmed!</p>
                
                <h3>Booking Details:</h3>
                <ul>
                    <li><strong>Booking ID:</strong> {bookingDetails.BookingID}</li>
                    <li><strong>Transaction ID:</strong> {transactionId}</li>
                    <li><strong>Trip Type:</strong> {bookingDetails.TripType}</li>
                    <li><strong>From:</strong> {bookingDetails.PickUpCity}</li>
                    <li><strong>To:</strong> {bookingDetails.DropOffCity}</li>
                    <li><strong>Pickup Date:</strong> {bookingDetails.PickUpTime}</li>
                    <li><strong>Car Category:</strong> {bookingDetails.CarCategory}</li>
                    <li><strong>Total Fare:</strong> ₹{bookingDetails.Fare}</li>
                </ul>
                
                <p>For any assistance, please contact our customer support.</p>
                <p>Thank you for choosing CabYaari!</p>
                ";

                var emailRequest = new EmailRequest
                {
                    From = "<EMAIL>",
                    To = bookingDetails.MailId,
                    Subject = $"CabYaari Booking Confirmation - {bookingDetails.BookingID}",
                    Body = emailBody
                };

                await _emailService.SendAsync(emailRequest);
            }

            private async Task SendBookingConfirmationSms(Domain.Entities.RLT_BOOKING booking)
            {
                try
                {
                    Console.WriteLine($"[CreateBookingPayment] Sending booking confirmation SMS to {booking.PhoneNumber}");

                    // Format pickup date and time
                    DateTime pickupDateTime;
                    if (bookingDetails.PickUpDate.HasValue && !string.IsNullOrEmpty(bookingDetails.PickUpTime))
                    {
                        // Combine pickup date and time
                        var pickupDate = bookingDetails.PickUpDate.Value.Date;
                        if (TimeSpan.TryParse(bookingDetails.PickUpTime, out TimeSpan pickupTime))
                        {
                            pickupDateTime = pickupDate.Add(pickupTime);
                        }
                        else
                        {
                            // If time parsing fails, use the date with current time
                            pickupDateTime = pickupDate.Add(DateTime.Now.TimeOfDay);
                        }
                    }
                    else
                    {
                        // Fallback to current date/time if pickup date/time is not available
                        pickupDateTime = DateTime.Now;
                    }
                    var formattedPickupDate = pickupDateTime.ToString("dd-MM-yyyy", CultureInfo.InvariantCulture);
                    var formattedPickupTime = pickupDateTime.ToString("hh:mm tt", CultureInfo.InvariantCulture);

                    // Format booking date and time (current date/time when booking was created)
                    var bookingDateTime = DateTime.Now;
                    var formattedBookingDate = bookingDateTime.ToString("dd-MM-yyyy", CultureInfo.InvariantCulture);
                    var formattedBookingTime = bookingDateTime.ToString("hh:mm tt", CultureInfo.InvariantCulture);

                    // Format payment details
                    var paymentDetails = FormatPaymentDetails(booking);

                    var bookingData = new BookingConfirmationSmsData
                    {
                        FromLocation = booking.PickUpCity ?? "N/A",
                        ToLocation = booking.DropOffCity ?? "N/A",
                        BookingDate = formattedBookingDate,
                        BookingTime = formattedBookingTime,
                        PickupDate = formattedPickupDate,
                        PickupTime = formattedPickupTime,
                        CabType = booking.CarCategory ?? "Cab",
                        PaymentDetails = paymentDetails,
                        PhoneNumber = booking.PhoneNumber
                    };

                    var smsSent = await _smsService.SendBookingConfirmationAsync(bookingData);

                    if (smsSent)
                    {
                        Console.WriteLine($"[CreateBookingPayment] Booking confirmation SMS sent successfully to {booking.PhoneNumber}");
                    }
                    else
                    {
                        Console.WriteLine($"[CreateBookingPayment] Failed to send booking confirmation SMS to {booking.PhoneNumber}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[CreateBookingPayment] Error sending booking confirmation SMS: {ex.Message}");
                }
            }

            private string FormatPaymentDetails(Domain.Entities.RLT_BOOKING booking)
            {
                var totalFare = booking.Fare ?? 0;
                var paymentType = booking.PaymentType ?? "FULL";

                if (paymentType == "PARTIAL")
                {
                    var paidAmount = booking.PartialPaymentAmount ?? 0;
                    var remainingAmount = booking.RemainingAmountForDriver ?? 0;
                    return $"INR{paidAmount:N0} paid online. Remaining INR{remainingAmount:N0} to be paid to driver.";
                }
                else
                {
                    // For full payment, show total amount paid and remaining as 0
                    return $"INR{totalFare:N0} paid online. Remaining INR0 to be paid to driver.";
                }
            }

            private async Task SendAdminBookingAlertSms(Domain.Entities.RLT_BOOKING booking)
            {
                try
                {
                    Console.WriteLine($"[CreateBookingPayment] Sending admin booking alert SMS to {_iCloudSmsSettings.AdminAlertNumber}");

                    var totalFare = booking.Fare ?? 0;
                    var paymentType = booking.PaymentType ?? "FULL";
                    var paidAmount = paymentType == "PARTIAL" ? (booking.PartialPaymentAmount ?? 0) : totalFare;

                    var adminAlertData = new AdminBookingAlertSmsData
                    {
                        CustomerName = booking.TravelerName ?? "N/A",
                        FromLocation = booking.PickUpCity ?? "N/A",
                        ToLocation = booking.DropOffCity ?? "N/A",
                        TotalFare = totalFare.ToString("N0"),
                        PaidAmount = paidAmount.ToString("N0"),
                        BookingId = booking.BookingID ?? "N/A",
                        AdminPhoneNumber = _iCloudSmsSettings.AdminAlertNumber
                    };

                    var smsSent = await _smsService.SendAdminBookingAlertAsync(adminAlertData);

                    if (smsSent)
                    {
                        Console.WriteLine($"[CreateBookingPayment] Admin booking alert SMS sent successfully to {_iCloudSmsSettings.AdminAlertNumber}");
                    }
                    else
                    {
                        Console.WriteLine($"[CreateBookingPayment] Failed to send admin booking alert SMS to {_iCloudSmsSettings.AdminAlertNumber}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[CreateBookingPayment] Error sending admin booking alert SMS: {ex.Message}");
                }
            }
        }
    }
}