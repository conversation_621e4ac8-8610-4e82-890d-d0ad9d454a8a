-- =============================================
-- Update Stored Procedure: usp_Booking_Update_V2
-- Description: Add Mode_Of_Payment_Id parameter to support payment method tracking
-- Date: 2025-07-19
-- =============================================

USE [CabYaari]
GO

-- Drop the existing procedure if it exists
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[usp_Booking_Update_V2]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[usp_Booking_Update_V2]
GO

-- Create the updated procedure with Mode_Of_Payment_Id parameter
CREATE PROCEDURE [dbo].[usp_Booking_Update_V2]
    @BookingId nvarchar(30),
    @RazorpayPaymentId nvarchar(30),
    @RazorpayOrderid nvarchar(30),
    @RazorpaySignature nvarchar(30),
    @RazorpayStatus nvarchar(30),
    -- New parameters for partial payment support
    @PaymentType nvarchar(10) NULL,
    @PartialPaymentAmount decimal(18,2) NULL,
    @RemainingAmountForDriver decimal(18,2) NULL,
    -- New parameter for payment method
    @ModeOfPaymentId int NULL
WITH RECOMPILE
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;

    DECLARE @errorMessage nvarchar(max),
           @ScriptRanOn datetime = GETDATE(),
           @TransactionId nvarchar(30),
           @BookingStatusId int,
           @BookingPKID int,
           @CurrentPaymentType nvarchar(10),
           @CurrentPartialAmount decimal(18,2),
           @CurrentRemainingAmount decimal(18,2)

    -- Get current payment information
    SELECT
        @CurrentPaymentType = PaymentType,
        @CurrentPartialAmount = PartialPaymentAmount,
        @CurrentRemainingAmount = RemainingAmountForDriver
    FROM RLT_BOOKING
    WHERE Booking_Id = @BookingId

    -- Get the appropriate booking status ID based on payment status
    SET @BookingStatusId = CASE
        WHEN @RazorpayStatus IN ('Paid', 'COMPLETED') THEN 2  -- Confirmed/Paid booking
        WHEN @RazorpayStatus IN ('Failed') THEN 6  -- Cancelled booking (from BookingStatus enum)
        ELSE 1  -- Pending booking
    END

    BEGIN TRANSACTION
        -- Update the booking record with payment information
        UPDATE RLT_BOOKING
        SET
            razorpay_payment_id = @RazorpayPaymentId,
            razorpay_signature = @RazorpaySignature,
            razorpay_status = @RazorpayStatus,
            Booking_Status_Id = @BookingStatusId,
            -- Update payment fields only if new values are provided
            PaymentType = ISNULL(@PaymentType, @CurrentPaymentType),
            PartialPaymentAmount = ISNULL(@PartialPaymentAmount, @CurrentPartialAmount),
            RemainingAmountForDriver = ISNULL(@RemainingAmountForDriver, @CurrentRemainingAmount),
            -- Update Mode_Of_Payment_Id if provided
            Mode_Of_Payment_Id = ISNULL(@ModeOfPaymentId, Mode_Of_Payment_Id),
            Updated_Date = GETDATE()
        WHERE
            Booking_Id = @BookingId

        -- Get the updated booking information for the response
        SELECT
            @TransactionId = razorpay_payment_id,
            @BookingPKID = PKID,
            @PaymentType = ISNULL(@PaymentType, PaymentType),
            @PartialPaymentAmount = ISNULL(@PartialPaymentAmount, PartialPaymentAmount),
            @RemainingAmountForDriver = ISNULL(@RemainingAmountForDriver, RemainingAmountForDriver)
        FROM
            RLT_BOOKING
        WHERE
            Booking_Id = @BookingId

        SET @errorMessage = CONVERT(nvarchar(max), GETDATE(), 21) + ' running usp_Booking_Update_V2 => committing sql transaction'
        RAISERROR(@errorMessage, 0, 1) WITH NOWAIT
    COMMIT TRANSACTION

    -- Return the booking and transaction information including payment details
    SELECT
        @BookingId AS BookingId,
        @TransactionId AS TransactionId,
        @RazorpayStatus AS PaymentStatus,
        @PaymentType AS PaymentType,
        @PartialPaymentAmount AS PartialPaymentAmount,
        @RemainingAmountForDriver AS RemainingAmountForDriver,
        @BookingStatusId AS BookingStatusId
END
GO

PRINT 'Stored procedure usp_Booking_Update_V2 updated successfully with Mode_Of_Payment_Id support!'

-- Test the updated procedure (optional - comment out if not needed)
/*
-- Example test call
EXEC [dbo].[usp_Booking_Update_V2] 
    @BookingId = 'CY-170725-95186',
    @RazorpayPaymentId = 'OM2507191244501182375267',
    @RazorpayOrderid = 'OMO2507191244501172375404',
    @RazorpaySignature = '',
    @RazorpayStatus = 'COMPLETED',
    @PaymentType = 'FULL',
    @PartialPaymentAmount = NULL,
    @RemainingAmountForDriver = 0.00,
    @ModeOfPaymentId = 2  -- Credit Card
*/
